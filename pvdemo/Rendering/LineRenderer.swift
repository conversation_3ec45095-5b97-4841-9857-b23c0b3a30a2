//
//  LineRenderer.swift
//  PuttVisionDemo
//
//  Created on 2025-01-09.
//  Copyright © 2025 PuttVision. All rights reserved.
//

import SceneKit
import UIKit
import simd

public final class LineRenderer {
    public init() {}

    public func makeLineNode(points: [simd_float3], maxPoints: Int = 120, color: UIColor = .systemBlue) -> SCNNode {
        let pts = downsample(points, max: maxPoints)
        let verts = pts.map { SCNVector3($0.x, $0.y, $0.z) }
        let src = SCNGeometrySource(vertices: verts)
        
        var idx: [UInt16] = []
        idx.reserveCapacity(max(0, verts.count-1) * 2)
        
        for i in 0..<(max(0, verts.count-1)) { 
            idx.append(UInt16(i))
            idx.append(UInt16(i+1)) 
        }
        
        let data = Data(bytes: idx, count: idx.count * MemoryLayout<UInt16>.size)
        let ele = SCNGeometryElement(data: data, primitiveType: .line, primitiveCount: idx.count/2, bytesPerIndex: MemoryLayout<UInt16>.size)
        let geo = SCNGeometry(sources: [src], elements: [ele])
        
        let mat = SCNMaterial()
        mat.diffuse.contents = color
        mat.lightingModel = .constant
        mat.isDoubleSided = true
        geo.materials = [mat]
        
        return SCNNode(geometry: geo)
    }

    private func downsample(_ pts: [simd_float3], max: Int) -> [simd_float3] {
        guard pts.count > max else { return pts }
        let step = max(1, pts.count / max)
        return pts.enumerated().compactMap { (i,p) in i % step == 0 ? p : nil }
    }
}
