//
//  ARViewController.swift
//  PuttVisionDemo
//
//  Created on 2025-01-09.
//  Copyright © 2025 PuttVision. All rights reserved.
//

import UIKit
import ARKit
import SceneKit

final class ARViewController: UIViewController, ARSCNViewDelegate {
    @IBOutlet weak var arView: ARSCNView!
    @IBOutlet weak var instructionLabel: UILabel!
    @IBOutlet weak var resetButton: UIButton!

    private let lidar = LiDARManager()
    private let roi = CorridorROISelector()
    private let analyzer = SlopeAnalyzer()
    private let smoother = ParameterSmoother(alpha: 0.25)
    private let optimizer = TrajectoryOptimizer()
    private let physics = PhysicsSimulator()
    private let lineRenderer = LineRenderer()

    private var ballAnchor: ARAnchor?
    private var holeAnchor: ARAnchor?
    private var trajNode: SCNNode?

    override func viewDidLoad() {
        super.viewDidLoad()
        setupAR()
        setupUI()
        setupGestures()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        lidar.configure(session: arView.session)
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        arView.session.pause()
    }

    private func setupAR() {
        arView.delegate = self
        arView.scene = SCNScene()
        arView.autoenablesDefaultLighting = true
        arView.automaticallyUpdatesLighting = true
    }
    
    private func setupUI() {
        instructionLabel.text = "点击标记球位置"
        instructionLabel.backgroundColor = UIColor.black.withAlphaComponent(0.7)
        instructionLabel.textColor = .white
        instructionLabel.layer.cornerRadius = 8
        instructionLabel.textAlignment = .center
        
        resetButton.backgroundColor = .systemRed
        resetButton.layer.cornerRadius = 8
        resetButton.setTitleColor(.white, for: .normal)
        resetButton.setTitle("重置", for: .normal)
    }
    
    private func setupGestures() {
        let tap = UITapGestureRecognizer(target: self, action: #selector(handleTap(_:)))
        arView.addGestureRecognizer(tap)
    }

    @objc private func handleTap(_ g: UITapGestureRecognizer) {
        let pt = g.location(in: arView)
        let results = arView.raycast(from: pt, allowing: .estimatedPlane, alignment: .any)
        guard let r = results.first else { 
            showMessage("请点击检测到的平面")
            return 
        }
        
        let T = r.worldTransform
        
        if ballAnchor == nil {
            ballAnchor = ARAnchor(transform: T)
            arView.session.add(anchor: ballAnchor!)
            instructionLabel.text = "点击标记球洞位置"
        } else if holeAnchor == nil {
            holeAnchor = ARAnchor(transform: T)
            arView.session.add(anchor: holeAnchor!)
            instructionLabel.text = "计算轨迹中…"
            computeAndRender()
        } else {
            resetAll()
        }
    }
    
    @IBAction func resetButtonTapped(_ sender: UIButton) {
        resetAll()
    }

    private func computeAndRender() {
        guard let frame = arView.session.currentFrame,
              let ballT = ballAnchor?.transform, 
              let holeT = holeAnchor?.transform else { 
            showMessage("无法获取位置信息")
            return 
        }
        
        let ball = simd_make_float3(ballT.columns.3)
        let hole = simd_make_float3(holeT.columns.3)
        
        // Validate distance
        let distance = simd_length(hole - ball)
        guard distance >= 0.5 && distance <= 15.0 else {
            showMessage("距离应在0.5-15米之间")
            return
        }
        
        // Build/limit point cloud
        let cloud = lidar.currentPointCloud
        print("Point cloud size: \(cloud.count)")
        
        let corridor = roi.selectCorridor(pointCloud: cloud, ball: ball, hole: hole, width: 0.45)
        print("Corridor points: \(corridor.count)")
        
        guard corridor.count >= 20 else { 
            showMessage("点云不足，请缓慢扫过球与洞路径")
            return 
        }
        
        // Rough fit then tighten by distance-to-plane (<= 8mm) and refit
        guard let rough = analyzer.fitPlane(points: corridor) else { 
            showMessage("平面拟合失败")
            return 
        }
        
        let tight = corridor.filter { p in 
            abs(p.y - (rough.a*p.x + rough.b*p.z + rough.c)) < 0.008 
        }
        
        guard let plane0 = analyzer.fitPlane(points: tight.isEmpty ? corridor : tight) else { 
            showMessage("平面拟合失败")
            return 
        }
        
        let plane = smoother.smooth(plane0)
        
        // Optimize angle & simulate
        let angle = optimizer.findOptimalAngle(ball: ball, hole: hole, plane: plane)
        let v0 = optimizer.estimateBaseSpeed(distance: simd_length(hole - ball))
        let traj = physics.simulate(initialPosition: ball, launchAngle: angle, launchSpeed: v0, plane: plane)
        
        // Render
        trajNode?.removeFromParentNode()
        trajNode = lineRenderer.makeLineNode(points: traj, color: .systemGreen)
        arView.scene.rootNode.addChildNode(trajNode!)
        
        instructionLabel.text = "轨迹已生成（再次点击重置）"
        
        // Check if trajectory hits hole
        if physics.checkHoleHit(trajectory: traj, hole: hole) {
            showMessage("预测：球会进洞！")
        }
    }

    private func resetAll() {
        if let a = ballAnchor { 
            arView.session.remove(anchor: a) 
        }
        if let a = holeAnchor { 
            arView.session.remove(anchor: a) 
        }
        
        ballAnchor = nil
        holeAnchor = nil
        trajNode?.removeFromParentNode()
        trajNode = nil
        smoother.reset()
        
        instructionLabel.text = "点击标记球位置"
    }
    
    private func showMessage(_ text: String) {
        DispatchQueue.main.async {
            self.instructionLabel.text = text
        }
    }

    // MARK: - ARSCNViewDelegate
    
    func renderer(_ renderer: SCNSceneRenderer, nodeFor anchor: ARAnchor) -> SCNNode? {
        let node = SCNNode()
        
        if anchor == ballAnchor {
            // Create ball marker
            let sphere = SCNSphere(radius: 0.02)
            sphere.firstMaterial?.diffuse.contents = UIColor.white
            sphere.firstMaterial?.emission.contents = UIColor.red
            node.geometry = sphere
        } else if anchor == holeAnchor {
            // Create hole marker
            let cylinder = SCNCylinder(radius: 0.054, height: 0.01)
            cylinder.firstMaterial?.diffuse.contents = UIColor.black
            cylinder.firstMaterial?.emission.contents = UIColor.yellow
            node.geometry = cylinder
        }
        
        return node
    }
}
