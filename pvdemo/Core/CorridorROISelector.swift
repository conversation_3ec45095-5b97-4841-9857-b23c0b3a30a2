//
//  CorridorROISelector.swift
//  PuttVisionDemo
//
//  Created on 2025-01-09.
//  Copyright © 2025 PuttVision. All rights reserved.
//

import Foundation
import simd

public final class CorridorROISelector {
    public init() {}

    public func selectCorridor(pointCloud: [simd_float3], ball: simd_float3, hole: simd_float3, width: Float = 0.45) -> [simd_float3] {
        let dir = simd_normalize(hole - ball)
        let L = simd_length(hole - ball)
        var pts: [simd_float3] = []
        pts.reserveCapacity(min(pointCloud.count, 8000))
        
        for p in pointCloud {
            let rel = p - ball
            let s = simd_dot(rel, dir) // along corridor
            if s < -0.5 || s > L + 0.5 { continue }
            let proj = ball + s * dir
            let lateral = simd_length(p - proj)
            if lateral <= width { 
                pts.append(p) 
            }
        }
        
        return twoPassFilter(pts)
    }

    private func twoPassFilter(_ pts: [simd_float3]) -> [simd_float3] {
        guard pts.count >= 10 else { return pts }
        
        // Median/MAD on y
        let ys = pts.map { $0.y }.sorted()
        let med = ys[ys.count/2]
        let mad = ys.map { abs($0 - med) }.sorted()[ys.count/2]
        let prelim = pts.filter { abs($0.y - med) <= max(0.003, 3*mad) } // clamp min 3mm
        
        // Return prelim; second-stage (distance-to-plane) filtering will be done after a rough fit in controller
        return prelim
    }
}
