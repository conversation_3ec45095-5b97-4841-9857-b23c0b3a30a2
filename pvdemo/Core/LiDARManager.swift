//
//  LiDARManager.swift
//  PuttVisionDemo
//
//  Created on 2025-01-09.
//  Copyright © 2025 PuttVision. All rights reserved.
//

import ARKit
import Foundation
import simd

public final class LiDARManager: NSObject, ARSessionDelegate {
    public private(set) var isLiDARAvailable = false
    public private(set) var currentPointCloud: [simd_float3] = []
    private weak var session: ARSession?

    public func configure(session: ARSession) {
        self.session = session
        let cfg = ARWorldTrackingConfiguration()
        cfg.worldAlignment = .gravity
        
        if ARWorldTrackingConfiguration.supportsSceneReconstruction(.mesh) {
            cfg.sceneReconstruction = .mesh
            isLiDARAvailable = true
        }
        
        if ARWorldTrackingConfiguration.supportsFrameSemantics(.sceneDepth) {
            cfg.frameSemantics.insert([.sceneDepth, .smoothedSceneDepth])
        }
        
        session.run(cfg, options: [.resetTracking, .removeExistingAnchors])
        session.delegate = self
    }

    public func session(_ session: ARSession, didUpdate frame: ARFrame) {
        // Build a sparse point cloud from depth; prefer mesh if anchors exist
        var points: [simd_float3] = []
        
        if let anchors = session.currentFrame?.anchors as? [ARMeshAnchor], !anchors.isEmpty {
            points = extractPointsFromMesh(anchors)
        } else {
            points = convertDepthToPointCloud(frame: frame, step: 3)
        }
        
        currentPointCloud = points
    }

    public func convertDepthToPointCloud(frame: ARFrame, step: Int = 3) -> [simd_float3] {
        guard let depth = frame.smoothedSceneDepth ?? frame.sceneDepth else { return [] }
        let depthMap = depth.depthMap
        
        CVPixelBufferLockBaseAddress(depthMap, .readOnly)
        defer { CVPixelBufferUnlockBaseAddress(depthMap, .readOnly) }
        
        let w = CVPixelBufferGetWidth(depthMap)
        let h = CVPixelBufferGetHeight(depthMap)
        guard let base = CVPixelBufferGetBaseAddress(depthMap) else { return [] }
        let D = base.bindMemory(to: Float32.self, capacity: w*h)
        
        let K = frame.camera.intrinsics
        let cx = K.columns.2.x, cy = K.columns.2.y  // 修正：cy不是cz
        let fx = K.columns.0.x, fy = K.columns.1.y  // 修正：fy不是fz
        let T = frame.camera.transform
        
        var pts: [simd_float3] = []
        pts.reserveCapacity((w/step)*(h/step))
        
        for v in stride(from: 0, to: h, by: step) {
            for u in stride(from: 0, to: w, by: step) {
                let z = D[v*w + u]
                if !(z > 0.05 && z < 10) { continue }
                
                // 修正：相机坐标系转换
                let xc = (Float(u) - cx)/fx * z
                let yc = (Float(v) - cy)/fy * z
                let pc = simd_float4(xc, -yc, z, 1)  // 修正：y轴翻转
                let pw = (T * pc).xyz
                pts.append(pw)
            }
        }
        
        return pts
    }

    public func extractPointsFromMesh(_ meshAnchors: [ARMeshAnchor]) -> [simd_float3] {
        var points: [simd_float3] = []
        
        for mesh in meshAnchors {
            let geo = mesh.geometry
            let vtx = geo.vertices
            guard vtx.stride >= MemoryLayout<simd_float3>.stride else { continue }
            
            let ptr = vtx.buffer.contents()
            let stride = vtx.stride
            let offset = vtx.offset
            let count = vtx.count
            
            for i in 0..<count {
                let addr = ptr.advanced(by: offset + i*stride)
                let local = addr.assumingMemoryBound(to: simd_float3.self).pointee
                let world = (mesh.transform * simd_float4(local, 1)).xyz
                points.append(world)
            }
        }
        
        return points
    }
}
