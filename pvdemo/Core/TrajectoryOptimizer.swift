//
//  TrajectoryOptimizer.swift
//  PuttVisionDemo
//
//  Created on 2025-01-09.
//  Copyright © 2025 PuttVision. All rights reserved.
//

import Foundation
import simd

public final class TrajectoryOptimizer {
    private let physics = PhysicsSimulator()
    public init() {}

    public func estimateBaseSpeed(distance: Float) -> Float {
        // Over-cup 0.3 m target, constant deceleration model
        let s = max(0, distance + 0.3)
        return sqrt(max(0, 2 * PhysicsConstants.rollingFriction * s))
    }

    public func findOptimalAngle(ball: simd_float3, hole: simd_float3, plane: PlaneEquation) -> Float {
        let dist = simd_length(hole - ball)
        let v0 = estimateBaseSpeed(distance: dist)
        
        // Coarse: -20°, 0°, 20°
        let coarseAngles: [Float] = [-20, 0, 20].map { $0 * .pi/180 }
        let scores = coarseAngles.map { evaluate(ball, hole, $0, v0, plane) }
        let bestI = scores.enumerated().min { $0.element < $1.element }!.offset
        
        var minA: Float, maxA: Float
        if bestI == 0 { 
            minA = -30 * .pi/180
            maxA = -10 * .pi/180 
        } else if bestI == 2 { 
            minA = 10 * .pi/180
            maxA = 30 * .pi/180 
        } else { 
            minA = -15 * .pi/180
            maxA = 15 * .pi/180 
        }
        
        return goldenSection(minA, maxA, ball, hole, v0, plane)
    }

    private func evaluate(_ ball: simd_float3, _ hole: simd_float3, _ angle: Float, _ speed: Float, _ plane: PlaneEquation) -> Float {
        let traj = physics.simulate(initialPosition: ball, launchAngle: angle, launchSpeed: speed, plane: plane)
        
        if physics.checkHoleHit(trajectory: traj, hole: hole) { 
            return 0 
        }
        
        var minD: Float = .infinity
        var flyPenalty: Float = 0
        
        for i in 1..<traj.count {
            let p = traj[i]
            let q = traj[i-1]
            let d = simd_length(simd_float2(p.x - hole.x, p.z - hole.z))
            minD = min(minD, d)
            
            if d < 0.1 {
                let v = simd_length(p - q) / PhysicsConstants.timeStep
                if v > 2.0 { 
                    flyPenalty += 10 
                }
            }
        }
        
        return minD + flyPenalty
    }

    private func goldenSection(_ amin: Float, _ amax: Float, _ ball: simd_float3, _ hole: simd_float3, _ speed: Float, _ plane: PlaneEquation) -> Float {
        let phi: Float = (1 + sqrt(5)) / 2
        var a = amin, b = amax
        var c = b - (b - a) / phi
        var d = a + (b - a) / phi
        
        while (b - a) > (0.01 * .pi/180) { // 0.01°
            let fc = evaluate(ball, hole, c, speed, plane)
            let fd = evaluate(ball, hole, d, speed, plane)
            
            if fc < fd { 
                b = d
                d = c
                c = b - (b - a) / phi 
            } else { 
                a = c
                c = d
                d = a + (b - a) / phi 
            }
        }
        
        return 0.5 * (a + b)
    }
}
