//
//  PhysicsConstants.swift
//  PuttVisionDemo
//
//  Created on 2025-01-09.
//  Copyright © 2025 PuttVision. All rights reserved.
//

import Foundation

public enum PhysicsConstants {
    public static let gravity: Float = 9.81
    public static var rollingFriction: Float = 0.9   // m/s^2, tunable (Stimp proxy)
    public static let minVelocity: Float = 0.05      // m/s stop threshold
    public static let holeRadius: Float = 0.054      // m (approx half of cup)
    public static let hitVelocity: Float = 0.8       // m/s max to count as made
    public static let timeStep: Float = 0.003        // s
    public static let maxSimTime: Float = 8.0        // s
}
