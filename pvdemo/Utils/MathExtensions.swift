//
//  MathExtensions.swift
//  PuttVisionDemo
//
//  Created on 2025-01-09.
//  Copyright © 2025 PuttVision. All rights reserved.
//

import Foundation
import simd

extension simd_float4x4 {
    var translation: simd_float3 { 
        simd_make_float3(columns.3) 
    }
}

extension simd_float3 {
    var xz: simd_float2 { 
        simd_float2(x, z) 
    }
    
    var xyz: simd_float3 { 
        return self 
    }
}

@inline(__always) func distanceXZ(_ a: simd_float3, _ b: simd_float3) -> Float {
    simd_length(simd_float2(a.x - b.x, a.z - b.z))
}
